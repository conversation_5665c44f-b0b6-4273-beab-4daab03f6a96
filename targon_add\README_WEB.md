# Targon 批量注册工具 - Web界面

## 🌟 功能特性

- 🎨 **美观的Web界面** - 现代化设计，响应式布局
- 🚀 **并行注册** - 支持多线程并发注册，提高效率
- 🔐 **自动2FA** - 自动启用2FA获取0.2美元奖励
- 📊 **实时监控** - 实时显示注册进度和状态
- 💾 **数据管理** - 自动保存账户信息到JSON文件
- 📥 **一键导出** - 支持导出API Keys到TXT文件
- 📜 **详细日志** - 实时显示注册过程日志

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动Web服务器
```bash
npm run web
```

### 3. 访问界面
打开浏览器访问: http://localhost:3000

## 📋 使用说明

### 注册设置
- **注册数量**: 1-50个账户
- **并发数**: 1-10个并发线程
- **注册模式**: 
  - 并行注册: 多线程同时注册，速度快
  - 串行注册: 逐个注册，更稳定

### 功能按钮
- **🚀 开始注册**: 开始批量注册任务
- **⏹️ 停止注册**: 停止当前注册任务
- **📥 导出API Keys**: 导出所有成功账户的API Key
- **🗑️ 清空记录**: 清空所有账户记录
- **🔄 刷新列表**: 刷新账户列表
- **📜 显示/隐藏日志**: 切换日志显示

### 状态监控
- **总计账户**: 已注册的账户总数
- **成功注册**: 成功注册的账户数量
- **注册失败**: 失败的账户数量
- **完成进度**: 当前任务的完成百分比

### 账户列表
显示所有注册的账户信息：
- 邮箱地址
- API Key (显示后8位)
- 2FA状态 (✅/❌)
- 账户余额
- 注册状态
- 注册时间

## 🔧 技术架构

### 前端
- **HTML5 + CSS3**: 现代化响应式界面
- **JavaScript ES6+**: 异步处理和实时更新
- **流式API**: 实时接收注册进度

### 后端
- **Node.js + Express**: Web服务器
- **异步并发**: Promise.allSettled实现并行注册
- **流式响应**: Server-Sent Events风格的实时更新

### 数据存储
- **JSON文件**: 本地存储账户信息
- **自动备份**: 每次注册自动保存

## 📁 文件结构

```
targon_add/
├── web-interface.html      # Web界面HTML
├── web-interface.js        # 前端JavaScript
├── web-server.js          # Express服务器
├── targon-register.js     # 注册核心逻辑
├── totp-generator.js      # TOTP验证码生成
├── package.json           # 项目配置
└── registered_accounts.json # 账户数据
```

## 🎯 注册流程

1. **邮箱检查** - 验证邮箱可用性
2. **账户创建** - 创建新账户
3. **邮件验证** - 等待并处理验证邮件
4. **账户登录** - 自动登录新账户
5. **获取信息** - 获取API Key和用户信息
6. **启用2FA** - 自动设置2FA获取奖励
7. **更新余额** - 获取最新余额信息
8. **保存数据** - 保存到本地文件

## 💡 使用技巧

1. **并发设置**: 建议并发数设置为3-5，避免触发限制
2. **网络稳定**: 确保网络连接稳定，避免注册中断
3. **定期备份**: 定期备份registered_accounts.json文件
4. **监控日志**: 关注日志信息，及时发现问题

## 🔒 安全说明

- 所有账户信息存储在本地
- API Key仅显示后8位，完整信息存储在文件中
- 支持一键导出，方便使用
- 建议定期更换和管理API Key

## 🆘 常见问题

**Q: 注册失败怎么办？**
A: 查看日志信息，通常是网络问题或邮箱服务问题，可以重试。

**Q: 如何提高成功率？**
A: 降低并发数，使用串行模式，确保网络稳定。

**Q: API Key在哪里？**
A: 在账户列表中显示后8位，完整Key可通过导出功能获取。

**Q: 2FA奖励没有到账？**
A: 等待几分钟后刷新，或检查日志中的错误信息。

## 📞 支持

如有问题，请查看日志信息或联系技术支持。
