// 配置文件
module.exports = {
    // Targon网站配置
    targon: {
        baseUrl: 'https://targon.com',
        apiEndpoints: {
            checkEmail: '/api/trpc/account.checkEmail,account.check2FA',
            createAccount: '/api/trpc/account.createAccount',
            getUserInfo: '/api/trpc/account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,keys.getApiKeys,notification.getNotifications',
            create2FA: '/api/trpc/account.createTwoFactorURI'
        }
    },
    
    // 临时邮箱配置
    email: {
        domain: 'umombiss.tk',
        apiBase: 'http://umombiss.tk',
        prefixLength: 8, // 邮箱前缀长度
        checkInterval: 5000, // 检查邮件间隔(毫秒)
        maxAttempts: 30 // 最大检查次数
    },
    
    // 密码配置
    password: {
        length: 8,
        chars: 'abcdefghijklmnopqrstuvwxyz0123456789'
    },
    
    // 注册配置
    register: {
        defaultDelay: 10000, // 默认注册间隔(毫秒)
        outputFile: 'registered_accounts.json',
        maxRetries: 3 // 最大重试次数
    },
    
    // 请求头配置
    headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    },
    
    // 验证链接正则表达式
    verificationLinkRegex: /https:\/\/targon\.com\/email-verification\/\?token=[a-zA-Z0-9]+/g,
    
    // 日志配置
    logging: {
        enabled: true,
        level: 'info' // debug, info, warn, error
    }
};
